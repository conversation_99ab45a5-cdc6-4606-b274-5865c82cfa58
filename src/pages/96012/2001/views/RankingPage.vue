<template>
  <div class="bg" v-if="isLoadingFinish">
    <div class="topAreaBox" :style="furnishStyles.rankTopAreaBg.value">
      <div class="rankPageTitleBox">
        <img class="rankPageTitle" :src="furnish.rankPageTitleImg" alt="">
      </div>
      <div class="kvBox">
        <!-- 当只有一张图片时，直接显示图片 -->
        <img v-if="furnish.rankKvList && furnish.rankKvList.length === 1"
             :src="furnish.rankKvList[0].image"
             alt=""
             @click="handleJump(furnish.rankKvList[0].jumpUrl)">

        <!-- 当有多张图片时，使用Swiper轮播 -->
        <div v-else-if="furnish.rankKvList && furnish.rankKvList.length > 1" class="swiper-container kv-swiper">
          <div class="swiper-wrapper">
            <div class="swiper-slide" v-for="(item, index) in furnish.rankKvList" :key="index">
              <img :src="item.image" alt="" @click="handleJump(item.jumpUrl)">
            </div>
          </div>
        </div>

        <!-- 当没有图片时，显示默认内容 -->
        <div v-else class="no-image">暂无图片</div>
      </div>
      <div class="rankTipsBox">
        <img class="rankTips" :src="furnish.rankTipsImg" alt="">
        <div :style="furnishStyles.rankRuleBtnColor.value">查看更多说明</div>
      </div>
      <div class="rankPrizeBox">
        <img class="line" src="https://img10.360buyimg.com/imgzone/jfs/t1/339666/17/27966/119/690811dcFd2a76150/cb01e58d4987ec86.png" alt="">
        <div class="titleBox">
          <img class="rankPrizeTitle" :src="furnish.rankPrizeTitle" alt="">
        </div>
        <div class="mainPrizeBox" ref="prizeBoxRef">
          <div class="prizeItem" :style="furnishStyles.rankPrizeItemBg.value" v-for="(item, index) in rankPrizesList" :key="index">
            <div class="prizeImgBg">
              <img :src="item.prizeImg" alt="">
            </div>
            <div class="prizeNameBox" :style="furnishStyles.rankPrizeNameColor.value">{{item.prizeName}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="rankTitleBox">
      <img class="rankTitle" :src="furnish.rankTitleImg" alt="">
      <div class="rankTitleTips" v-if="imgList.length > 0" :style="furnishStyles.rankExplanationTextColor.value">
        <div>仅作阶段性榜单展示，非最终评奖榜单</div>
        <div>数据更新时间：XXXX.XX.XX</div>
      </div>
    </div>
    <div class="showImgArea" v-if="imgList.length > 0">
      <ImagesList
        :imgList="imgList"
        :showUserInfo="true"
        :showRank="true"
        :showChangeShare="false"
        :hasMoreData="hasMoreData"
        :isLoadingMore="isLoadingMore"
        @loadMore="handleLoadMore"
      ></ImagesList>
    </div>
    <div v-else class="no-data">暂无数据</div>
    <div class="rankBottomArea" v-if="beforeRankList.length > 0">
      <div class="beforeTitle">往期展示</div>
      <div class="backBefore">
        <div :style="furnishStyles.drawDateTextColor.value">开奖日期：{{openDate ? openDate : 'XXXX.XX.XX'}}</div>
        <div :style="furnishStyles.backTextColor.value" @click="handleBack()">⬅返回该期活动</div>
      </div>
    </div>
    <div class="beforeRankBox" v-if="beforeRankList.length > 0">
      <div class="beforeRankItem" v-for="(item, index) in beforeRankList" :key="index">
        <div class="imgBox">
          <div class="rankNum">{{item.rank}}</div>
          <img class="beforeRankImg" :src="item.imgList[0]" alt="">
        </div>
        <div class="nickName">{{item.nickName}}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, nextTick, computed, defineProps, inject, onUnmounted, watch} from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import Swiper from 'swiper';
import { Autoplay, Pagination } from 'swiper';
import 'swiper/swiper.min.css';
import ImagesList from "../components/ImagesList.vue";
import {closeToast, showLoadingToast} from "vant";
import {checkActTime} from "../ts/logic";
import { httpRequest } from "@/utils/service";
import { BaseInfo } from "@/types/BaseInfo";

// 定义奖品项接口
interface PrizeItem {
  prizeImg: string;
  prizeName: string;
  // 可以根据实际接口返回添加更多字段
}

// 定义图片列表项接口
interface ImageListItem {
  imgList: string[];
  title: string;
  content: string;
  avatar: string;
  nickName: string;
  fans: number;
  createTime: string;
  liked: boolean;
  rank?: number;
  dynamicId?: string;
}

// 定义往期排名项接口
interface BeforeRankItem {
  imgList: string[];
  nickName: string;
  rank: number;
  title: string;
  content: string;
  avatar: string;
  createTime: string;
  liked: boolean;
  fans: number;
}

const baseInfo = inject("baseInfo") as BaseInfo;
const imgList = ref<ImageListItem[]>([]);

// 分页相关状态
const currentPage = ref(1);
const pageSize = ref(20);
const hasMoreData = ref(true);
const isLoadingMore = ref(false);

const beforeRankList = ref<BeforeRankItem[]>([]);

// 奖品滚动相关
const prizeBoxRef = ref<HTMLElement | null>(null);
let animationFrameId: number | null = null;

const rankPrizesList = ref<PrizeItem[]>([]);
const myRankPrize = async () => {
  try {
    const { data } = await httpRequest.post('/96012/getPrizes',{
      type: 2,// 类型 1-抽奖奖品 2-排行奖品
    });
    rankPrizesList.value = data;
  } catch (e: any){
    console.log(e.message)
  }
}
//	往期活动链接
const oldActivityLink = ref('');
// 开奖日期
const openDate = ref('');
// 往期展示
const oldRankInfo = async () => {
  try {
    const { data } = await httpRequest.post('/96012/oldRankInfo');
    beforeRankList.value = data.dynamicList;
    oldActivityLink.value = data.oldActivityLink;
    openDate.value = data.openDate;
  } catch (e: any){
    console.log(e.message)
  }
}

// 返回往期
const handleBack = () => {
  // 目前是用从装修里拿的链接
  window.location.href = furnish.backUrl;
  // 暂时先不用这种，后续需要修改的话再改成这个
  // window.location.href = oldActivityLink.value;
}

// 重置分页状态
const resetPagination = () => {
  currentPage.value = 1;
  hasMoreData.value = true;
  isLoadingMore.value = false;
};

// 获取人气排行榜
const getHotRank = async (isLoadMore = false) => {
  try {
    // 如果是加载更多，设置加载状态
    if (isLoadMore) {
      isLoadingMore.value = true;
    }

    const { data } = await httpRequest.post('/96012/rankList', {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    });

    if (isLoadMore) {
      // 加载更多时追加数据
      imgList.value = [...imgList.value, ...data];
    } else {
      // 首次加载时替换数据
      imgList.value = data;
    }

    // 检查是否还有更多数据
    hasMoreData.value = data.length === pageSize.value;

  } catch (e: any){
    console.log(e.message)
  } finally {
    if (isLoadMore) {
      isLoadingMore.value = false;
    }
  }
};

// 处理加载更多
const handleLoadMore = () => {
  if (hasMoreData.value && !isLoadingMore.value) {
    currentPage.value++;
    getHotRank(true);
  }
};

// 处理跳转
const handleJump = (jumpUrl: string) => {
  if (jumpUrl) {
    window.location.href = jumpUrl;
  }
}

// 开始奖品自动滚动
const startPrizeScroll = () => {
  // 停止之前的滚动
  stopPrizeScroll();

  if (!prizeBoxRef.value || rankPrizesList.value.length <= 3) return;

  const prizeBox = prizeBoxRef.value;
  let scrollPosition = 0;
  const scrollSpeed = 1; // 滚动速度

  // 先复制奖品项以实现无缝滚动效果
  const clonePrizeItems = () => {
    // 清除之前克隆的元素
    const clonedItems = prizeBox.querySelectorAll('.prizeItem[data-cloned]');
    clonedItems.forEach(item => item.remove());

    // 克隆所有奖品项
    const prizeItems = prizeBox.querySelectorAll('.prizeItem:not([data-cloned])');
    prizeItems.forEach(item => {
      const clone = item.cloneNode(true) as HTMLElement;
      clone.setAttribute('data-cloned', 'true');
      prizeBox.appendChild(clone);
    });
  };

  const scroll = () => {
    scrollPosition += scrollSpeed;
    if (scrollPosition >= prizeBox.scrollWidth / 2) {
      scrollPosition = 0;
      prizeBox.scrollLeft = 0; // 重置滚动位置以确保平滑过渡
    }
    prizeBox.scrollLeft = scrollPosition;
    animationFrameId = requestAnimationFrame(scroll);
  };

  // 等待DOM更新后执行
  nextTick(() => {
    clonePrizeItems();
    animationFrameId = requestAnimationFrame(scroll);
  });
};

// 停止奖品滚动
const stopPrizeScroll = () => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }
};

// 监听奖品列表变化，重新启动滚动
watch(rankPrizesList, () => {
  nextTick(() => {
    startPrizeScroll();
  });
});

// Swiper 实例
let swiperInstance: Swiper | null = null;

// 初始化 Swiper
const initSwiper = () => {
  nextTick(() => {
    const swiperElement = document.querySelector('.kv-swiper');
    if (swiperElement && furnish.kvList && furnish.kvList.length > 1) {
      // 销毁旧实例
      if (swiperInstance) {
        swiperInstance.destroy(true, true);
      }
      // 创建新实例
      swiperInstance = new Swiper(swiperElement as HTMLElement, {
        modules: [Autoplay, Pagination],
        slidesPerView: 1,
        spaceBetween: 0,
        loop: true,
        autoplay: {
          delay: 3000,
          disableOnInteraction: false
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        }
      });
    }
  });
};

// 销毁 Swiper 实例
const destroySwiper = () => {
  if (swiperInstance) {
    swiperInstance.destroy(true, true);
    swiperInstance = null;
  }
};

// 组件挂载后初始化 Swiper
onMounted(() => {
  // 使用延迟初始化，确保 DOM 更新完成
  setTimeout(() => {
    initSwiper();
    startPrizeScroll(); // 启动奖品滚动
  }, 200);
});

// 组件卸载前清理定时器
onUnmounted(() => {
  stopPrizeScroll();
});

const isLoadingFinish = ref(false);
const initRankingPage = async () => {
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([myRankPrize(), oldRankInfo(),getHotRank()]);
    isLoadingFinish.value = true;
    closeToast();
    if (!checkActTime(baseInfo)) {
      return;
    }
  } catch (error: any) {
    console.error(error);
    closeToast();
  }
};
initRankingPage();
</script>
<style>
@font-face {
  font-family: 'vivoSans-Regular';
  src: url(../assets/vivoSans-Regular.ttf) format('ttf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
</style>
<style scoped lang="scss">
.bg {
  width: 7.5rem;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding: 0 0 0.8rem 0;

  .topAreaBox{
    border-radius: 0 0 0.5rem 0.5rem;
    padding: 0 0 0.3rem 0;
    .rankPageTitleBox{
      padding: 0.5rem;
      .rankPageTitle{
        width: 100%;
      }
    }
  }

}

.kvBox {
  width: 7.5rem;
  position: relative;

  img {
    width: 100%;
    display: block;
  }

  .no-image {
    height: 4.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
  }
}

.rankTipsBox {
  padding: 0 0.5rem 0.5rem;
  text-align: center;
  font-size: 0.24rem;
  .rankTips{
    margin-bottom: 0.14rem;
    width: 100%;
  }
}

.rankPrizeBox{
  .line {
    width: 6.88rem;
    margin: 0 auto 0.6rem;
  }
  .titleBox{
    padding: 0 0.9rem;
    .rankPrizeTitle{
      width: 100%;
    }
  }
  .mainPrizeBox{
    display: flex;
    width: 7.2rem;
    min-height: 3.3rem;
    /* justify-content: space-between; */
    justify-content: center;
    margin: 0 0 0 0.3rem;
    padding: 0.26rem 0 0 0;
    overflow: hidden;
    overflow-x: hidden; // 隐藏滚动条
    position: relative;
    .prizeItem{
      width: 2.2rem;
      height: 2.8rem;
      text-align: center;
      margin-right: 0.2rem;
      font-size: 0.22rem;
      border-radius: 0.2rem;
      flex-shrink: 0; // 防止压缩
      .prizeImgBg{
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.08rem;
        img{
          width: 1.5rem;
          height: 1.5rem;
          margin: 0 auto;
        }
      }
      .prizeNameBox{
        width: 2rem;
        height: 0.7rem;
        margin: 0.2rem auto;
        overflow: hidden;
        word-break: break-all;
        text-align: center;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
      }
    }
  }
}
.rankTitleBox{
  padding: 0.8rem 0.6rem 0.2rem;
  .rankTitle{
    width: 100%;
    margin-bottom: 0.3rem;
  }
  .rankTitleTips{
    text-align: center;
    font-size: 0.26rem;
    line-height: 0.32rem;
  }
}

.kv-swiper {
  width: 7.5rem;
  height: 100%;

  .swiper-slide {
    img {
      width: 100%;
      display: block;
    }
  }

  .swiper-pagination {
    bottom: 10px;

    :deep(.swiper-pagination-bullet) {
      width: 8px;
      height: 8px;
      background: rgba(255, 255, 255, 0.5);
      opacity: 1;
    }

    :deep(.swiper-pagination-bullet-active) {
      background: #fff;
    }
  }
}
.showImgArea{
  width: 7.1rem;
  margin: 0 auto;
  padding: 0 0 0.73rem;
}
.no-data{
  width: 100%;
  text-align: center;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 2.5rem;
  font-size: 0.24rem;
}
.rankBottomArea{
  width: 6.92rem;
  margin: 0 auto;
  text-align: center;
  .beforeTitle{
    font-family: vivoSans-Regular;
    font-size: 0.48rem;
  }
  .backBefore{
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.24rem;
  }
}
.beforeRankBox{
  width: 6.9rem;
  margin: 0.2rem auto 0;
  display: flex;
  align-items: center;
  overflow: hidden;
  overflow-x: scroll;
  &::-webkit-scrollbar {
    display: none;
  }
  .beforeRankItem{
    height: 2.6rem;
    border-radius: 0.15rem;
    margin: 0 0.3rem 0.3rem 0;
    background: #fff;
    padding: 0.2rem 0.15rem;
    .rankNum{
      position: absolute;
      top: -0.02rem;
      left: -0.02rem;
      background: url(https://img10.360buyimg.com/imgzone/jfs/t1/354654/16/1543/1166/68ff0914F9f0d777d/af84330388c65dd4.png) no-repeat;
      background-size: 100%;
      width: 0.7rem;
      height: 0.7rem;
      padding: 0.1rem 0.37rem 0 0.095rem;
      color: #dca45c;
      font-size: 0.18rem;
      font-weight: bold;
      text-align: center;
    }
    .imgBox{
      position: relative;
      width: 1.8rem;
      height: 1.8rem;
      overflow: hidden;
      border-radius: 0.1rem;
      .beforeRankImg{
        width: 1.8rem;
        height: auto;
        display: block;
        margin: 0 auto;
      }
    }
    .nickName{
      font-size: 0.22rem;
      margin: 0.1rem 0;
      color: #666;
    }
  }
}
</style>
