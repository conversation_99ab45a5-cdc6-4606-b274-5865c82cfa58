<template>
  <div class="bg">
    <div class="topAreaBox" :style="furnishStyles.rankTopAreaBg.value">
      <div class="rankPageTitleBox">
        <img class="rankPageTitle" :src="furnish.rankPageTitleImg" alt="">
      </div>
      <div class="kvBox">
        <!-- 当只有一张图片时，直接显示图片 -->
        <img v-if="furnish.rankKvList && furnish.rankKvList.length === 1"
             :src="furnish.rankKvList[0].image"
             alt=""
             @click="handleJump(furnish.rankKvList[0].jumpUrl)">

        <!-- 当有多张图片时，使用Swiper轮播 -->
        <div v-else-if="furnish.rankKvList && furnish.rankKvList.length > 1" class="swiper-container kv-swiper">
          <div class="swiper-wrapper">
            <div class="swiper-slide" v-for="(item, index) in furnish.rankKvList" :key="index">
              <img :src="item.image" alt="" @click="handleJump(item.jumpUrl)">
            </div>
          </div>
        </div>

        <!-- 当没有图片时，显示默认内容 -->
        <div v-else class="no-image">暂无图片</div>
      </div>
      <div class="rankTipsBox">
        <img class="rankTips" :src="furnish.rankTipsImg" alt="">
        <div :style="furnishStyles.rankRuleBtnColor.value">查看更多说明</div>
      </div>
      <div class="rankPrizeBox">
        <img class="line" src="https://img10.360buyimg.com/imgzone/jfs/t1/339666/17/27966/119/690811dcFd2a76150/cb01e58d4987ec86.png" alt="">
        <div class="titleBox">
          <img class="rankPrizeTitle" :src="furnish.rankPrizeTitle" alt="">
        </div>
        <div class="mainPrizeBox" ref="prizeBoxRef">
          <div class="prizeItem" :style="furnishStyles.rankPrizeItemBg.value" v-for="(item, index) in filteredRankPrizesList" :key="index">
            <div class="prizeImgBg">
              <img :src="item.prizeImg" alt="">
            </div>
            <div class="prizeNameBox" :style="furnishStyles.rankPrizeNameColor.value">{{item.prizeName}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="rankTitleBox">
      <img class="rankTitle" :src="furnish.rankTitleImg" alt="">
      <div class="rankTitleTips" :style="furnishStyles.rankExplanationTextColor.value">
        <div>仅作阶段性榜单展示，非最终评奖榜单</div>
        <div>数据更新时间：XXXX.XX.XX</div>
      </div>
    </div>
    <div class="showImgArea">
      <ImagesList :imgList="imgList" :showUserInfo="true" :showRank="true" :showChangeShare="false"></ImagesList>
    </div>
    <div class="rankBottomArea">
      <div class="beforeTitle">往期展示</div>
      <div class="backBefore">
        <div :style="furnishStyles.drawDateTextColor.value">开奖日期：XXXX.XX.XX</div>
        <div :style="furnishStyles.backTextColor.value">⬅返回该期活动</div>
      </div>
    </div>
    <div class="beforeRankBox">
      <div class="beforeRankItem" v-for="(item, index) in beforRankList" :key="index">
        <div class="imgBox">
          <div class="rankNum">{{item.rank}}</div>
          <img class="beforeRankImg" :src="item.imgList[0]" alt="">
        </div>
        <div class="nickName">{{item.nickName}}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, nextTick, computed, defineProps, onUnmounted, watch} from 'vue';
import furnishStyles, { furnish } from '../../ts/furnishStyles';
import Swiper from 'swiper';
import { Autoplay, Pagination } from 'swiper';
import 'swiper/swiper.min.css';
import ImagesList from "../../components/ImagesList.vue";

const props = defineProps(['rankPrizesList']);

// 过滤掉下架的奖品（status === 0）
const filteredRankPrizesList = computed(() => {
  const prizeList = props.rankPrizesList || [];
  return prizeList.filter(item => item.status !== 0);
});

// 奖品滚动相关
const prizeBoxRef = ref<HTMLElement | null>(null);
let animationFrameId: number | null = null;

const imgList = ref([
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
    ],
    title: '标题1',
    content: '描述1',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户1',
    fans: 1000,
    createTime: '2025-11-20',
    liked: true,
    rank: 1,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题2',
    content: '描述2',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户2',
    fans: 10009,
    createTime: '2025-11-20',
    liked: false,
    rank: 2,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/291461/39/27351/11615/69046e4cF2e58da62/e0ba47c13aa9d89d.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题3',
    content: '描述3',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户3',
    fans: 1000,
    createTime: '2025-11-20',
    liked: false,
    rank: 3,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/353678/3/3990/29426/69046e4bF1b25c9cf/1eb61a31219e6fef.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题4',
    content: '描述4',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户4',
    fans: 1000,
    createTime: '2025-11-20',
    liked: false,
    rank: 4,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题5',
    content: '描述5',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户5',
    fans: 1000,
    createTime: '2025-11-20',
    liked: false,
    rank: 5,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/353678/3/3990/29426/69046e4bF1b25c9cf/1eb61a31219e6fef.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
    ],
    title: '标题6',
    content: '描述6',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    nickName: '用户6',
    fans: 1000,
    createTime: '2025-11-20',
    liked: false,
    rank: 6,
  },
]);

const beforRankList = ref([
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
    ],
    nickName: '用户1',
    rank: 1,
    title: '标题1',
    content: '描述1',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    createTime: '2025-11-20',
    liked: true,
    fans: 1000,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
    ],
    nickName: '用户2',
    rank: 2,
    title: '标题2',
    content: '描述2',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    createTime: '2025-11-20',
    liked: true,
    fans: 1000,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
    ],
    nickName: '用户3',
    rank: 3,
    title: '标题3',
    content: '描述3',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    createTime: '2025-11-20',
    liked: true,
    fans: 1000,
  },
  {
    imgList: [
      'https://img10.360buyimg.com/imgzone/jfs/t1/341674/33/19023/43281/69043176F72d1d811/18447e8fa825462a.png',
      'https://img10.360buyimg.com/imgzone/jfs/t1/346008/19/19505/32240/69046e4cF6c12c4bf/475070893f25b40c.png',
    ],
    nickName: '用户4',
    rank: 4,
    title: '标题4',
    content: '描述4',
    avatar: 'https://img10.360buyimg.com/imgzone/jfs/t1/350868/34/19226/3159/69044f8aF46751377/fe651ec133a90fb4.png',
    createTime: '2025-11-20',
    liked: true,
    fans: 1000,
  },
]);

// Swiper 实例
let swiperInstance: Swiper | null = null;

// 初始化 Swiper
const initSwiper = () => {
  nextTick(() => {
    const swiperElement = document.querySelector('.kv-swiper');
    if (swiperElement && furnish.kvList && furnish.kvList.length > 1) {
      // 销毁旧实例
      if (swiperInstance) {
        swiperInstance.destroy(true, true);
      }
      // 创建新实例
      swiperInstance = new Swiper(swiperElement as HTMLElement, {
        modules: [Autoplay, Pagination],
        slidesPerView: 1,
        spaceBetween: 0,
        loop: true,
        autoplay: {
          delay: 3000,
          disableOnInteraction: false
        },
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        }
      });
    }
  });
};

// 销毁 Swiper 实例
const destroySwiper = () => {
  if (swiperInstance) {
    swiperInstance.destroy(true, true);
    swiperInstance = null;
  }
};

// 开始奖品自动滚动
const startPrizeScroll = () => {
  // 停止之前的滚动
  stopPrizeScroll();

  if (!prizeBoxRef.value || !filteredRankPrizesList.value || filteredRankPrizesList.value.length <= 3) return;

  const prizeBox = prizeBoxRef.value;
  let scrollPosition = 0;
  const scrollSpeed = 1; // 滚动速度

  // 先复制奖品项以实现无缝滚动效果
  const clonePrizeItems = () => {
    // 清除之前克隆的元素
    const clonedItems = prizeBox.querySelectorAll('.prizeItem[data-cloned]');
    clonedItems.forEach(item => item.remove());

    // 克隆所有奖品项
    const prizeItems = prizeBox.querySelectorAll('.prizeItem:not([data-cloned])');
    prizeItems.forEach(item => {
      const clone = item.cloneNode(true) as HTMLElement;
      clone.setAttribute('data-cloned', 'true');
      prizeBox.appendChild(clone);
    });
  };

  const scroll = () => {
    scrollPosition += scrollSpeed;
    if (scrollPosition >= prizeBox.scrollWidth / 2) {
      scrollPosition = 0;
      prizeBox.scrollLeft = 0; // 重置滚动位置以确保平滑过渡
    }
    prizeBox.scrollLeft = scrollPosition;
    animationFrameId = requestAnimationFrame(scroll);
  };

  // 等待DOM更新后执行
  nextTick(() => {
    clonePrizeItems();
    animationFrameId = requestAnimationFrame(scroll);
  });
};

// 停止奖品滚动
const stopPrizeScroll = () => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }
};

// 处理跳转
const handleJump = (jumpUrl: string) => {
  if (jumpUrl) {
    window.location.href = jumpUrl;
  }
};

// 监听奖品列表变化，重新启动滚动
watch(filteredRankPrizesList, () => {
  nextTick(() => {
    startPrizeScroll();
  });
});

// 组件挂载后初始化 Swiper
onMounted(() => {
  // 使用延迟初始化，确保 DOM 更新完成
  setTimeout(() => {
    initSwiper();
    startPrizeScroll(); // 启动奖品滚动
  }, 200);
});

// 组件卸载前清理定时器
onUnmounted(() => {
  stopPrizeScroll();
});
</script>
<style>
@font-face {
  font-family: 'vivoSans-Regular';
  src: url(../../assets/vivoSans-Regular.ttf) format('ttf');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
</style>
<style scoped lang="scss">
.bg {
  width: 7.5rem;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding: 0 0 0.8rem 0;

  .topAreaBox{
    border-radius: 0 0 0.5rem 0.5rem;
    padding: 0 0 0.3rem 0;
    .rankPageTitleBox{
      padding: 0.5rem;
      .rankPageTitle{
        width: 100%;
      }
    }
  }

}

.kvBox {
  width: 7.5rem;
  position: relative;

  img {
    width: 100%;
    display: block;
  }

  .no-image {
    height: 4.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
  }
}

.rankTipsBox {
  padding: 0 0.5rem 0.5rem;
  text-align: center;
  font-size: 0.24rem;
  .rankTips{
    margin-bottom: 0.14rem;
    width: 100%;
  }
}

.rankPrizeBox{
  .line {
    width: 6.88rem;
    margin: 0 auto 0.6rem;
  }
  .titleBox{
    padding: 0 0.9rem;
    .rankPrizeTitle{
      width: 100%;
    }
  }
  .mainPrizeBox{
    display: flex;
    width: 7.2rem;
    min-height: 3.3rem;
    /* justify-content: space-between; */
    justify-content: center;
    margin: 0 0 0 0.3rem;
    padding: 0.26rem 0 0 0;
    overflow: hidden;
    overflow-x: hidden; // 隐藏滚动条
    position: relative;
    .prizeItem{
      width: 2.2rem;
      height: 2.8rem;
      text-align: center;
      margin-right: 0.2rem;
      font-size: 0.22rem;
      border-radius: 0.2rem;
      flex-shrink: 0; // 防止压缩
      .prizeImgBg{
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.08rem;
        img{
          width: 1.5rem;
          height: 1.5rem;
          margin: 0 auto;
        }
      }
      .prizeNameBox{
        width: 2rem;
        height: 0.7rem;
        margin: 0.2rem auto;
        overflow: hidden;
        word-break: break-all;
        text-align: center;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
      }
    }
  }
}
.rankTitleBox{
  padding: 0.8rem 0.6rem 0.2rem;
  .rankTitle{
    width: 100%;
    margin-bottom: 0.3rem;
  }
  .rankTitleTips{
    text-align: center;
    font-size: 0.26rem;
    line-height: 0.32rem;
  }
}

.kv-swiper {
  width: 7.5rem;
  height: 100%;

  .swiper-slide {
    img {
      width: 100%;
      display: block;
    }
  }

  .swiper-pagination {
    bottom: 10px;

    :deep(.swiper-pagination-bullet) {
      width: 8px;
      height: 8px;
      background: rgba(255, 255, 255, 0.5);
      opacity: 1;
    }

    :deep(.swiper-pagination-bullet-active) {
      background: #fff;
    }
  }
}
.showImgArea{
  width: 7.1rem;
  margin: 0 auto;
  padding: 0 0 0.73rem;
}
.rankBottomArea{
  width: 6.92rem;
  margin: 0 auto;
  text-align: center;
  .beforeTitle{
    font-family: vivoSans-Regular;
    font-size: 0.48rem;
  }
  .backBefore{
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.24rem;
  }
}
.beforeRankBox{
  width: 6.9rem;
  margin: 0.2rem auto 0;
  display: flex;
  align-items: center;
  overflow: hidden;
  overflow-x: scroll;
  &::-webkit-scrollbar {
    display: none;
  }
  .beforeRankItem{
    height: 2.6rem;
    border-radius: 0.15rem;
    margin: 0 0.3rem 0.3rem 0;
    background: #fff;
    padding: 0.2rem 0.15rem;
    .rankNum{
      position: absolute;
      top: -0.02rem;
      left: -0.02rem;
      background: url(https://img10.360buyimg.com/imgzone/jfs/t1/354654/16/1543/1166/68ff0914F9f0d777d/af84330388c65dd4.png) no-repeat;
      background-size: 100%;
      width: 0.7rem;
      height: 0.7rem;
      padding: 0.1rem 0.37rem 0 0.095rem;
      color: #dca45c;
      font-size: 0.18rem;
      font-weight: bold;
      text-align: center;
    }
    .imgBox{
      position: relative;
      width: 1.8rem;
      height: 1.8rem;
      overflow: hidden;
      border-radius: 0.1rem;
      .beforeRankImg{
        width: 1.8rem;
        height: auto;
        display: block;
        margin: 0 auto;
      }
    }
    .nickName{
      font-size: 0.22rem;
      margin: 0.1rem 0;
      color: #666;
    }
  }
}
</style>
